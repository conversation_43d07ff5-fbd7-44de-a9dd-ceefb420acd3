#ifndef _KEY_H
#define _KEY_H
#include "ti_msp_dl_config.h"
#include "bsp_system.h"

#define KEY  DL_GPIO_readPins(KEY_PORT,KEY_key_PIN)

// 按键防抖参数定义
#define KEY_DEBOUNCE_TIME    20    // 防抖时间 20ms
#define KEY_LONG_PRESS_TIME  1000  // 长按时间 1000ms

// 按键状态枚举
typedef enum {
    KEY_STATE_IDLE = 0,      // 空闲状态
    KEY_STATE_DEBOUNCE,      // 防抖状态
    KEY_STATE_PRESSED,       // 按下状态
    KEY_STATE_LONG_PRESS,    // 长按状态
    KEY_STATE_RELEASE        // 释放状态
} key_state_t;

// 按键事件枚举
typedef enum {
    KEY_EVENT_NONE = 0,      // 无事件
    KEY_EVENT_PRESS,         // 短按事件
    KEY_EVENT_LONG_PRESS,    // 长按事件
    KEY_EVENT_RELEASE        // 释放事件
} key_event_t;

// 按键控制结构体
typedef struct {
    key_state_t state;       // 当前状态
    uint32_t timer;          // 计时器
    uint8_t last_level;      // 上次电平状态
    uint8_t press_flag;      // 按下标志
    uint8_t long_press_flag; // 长按标志
} key_ctrl_t;

// 函数声明
void Key(void);
void Key_1(void);
key_event_t Key_Scan_Debounce(key_ctrl_t *key_ctrl, uint8_t current_level);
void Key_Init_Debounce(void);
void Key_System_Tick_Inc(void);

// 外部变量声明
extern volatile int Flag_stop;
extern volatile int Flag_stop1;
extern key_ctrl_t key1_ctrl;  // PA15按键控制结构体

#endif
