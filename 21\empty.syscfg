/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 */
const ADC12   = scripting.addModule("/ti/driverlib/ADC12", {}, false);
const ADC121  = ADC12.addInstance();
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const GPIO4   = GPIO.addInstance();
const GPIO5   = GPIO.addInstance();
const GPIO6   = GPIO.addInstance();
const GPIO7   = GPIO.addInstance();
const GPIO8   = GPIO.addInstance();
const GPIO9   = GPIO.addInstance();
const I2C     = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1    = I2C.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const TIMER   = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1  = TIMER.addInstance();
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider7       = system.clockTree["PLL_PDIV"];
divider7.divideValue = 2;

const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 5;

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL2X";

ADC121.$name                      = "ADC12_0";
ADC121.sampClkDiv                 = "DL_ADC12_CLOCK_DIVIDE_8";
ADC121.powerDownMode              = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC121.sampleTime0                = "1us";
ADC121.peripheral.$assign         = "ADC0";
ADC121.peripheral.adcPin0.$assign = "PA27";
ADC121.adcPin0Config.$name        = "ti_driverlib_gpio_GPIOPinGeneric2";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO1.port                          = "PORTB";
GPIO1.$name                         = "LED";
GPIO1.associatedPins[0].$name       = "PIN_22";
GPIO1.associatedPins[0].assignedPin = "22";

GPIO2.$name                         = "AIN";
GPIO2.port                          = "PORTA";
GPIO2.associatedPins.create(2);
GPIO2.associatedPins[0].$name       = "AIN1";
GPIO2.associatedPins[0].assignedPin = "14";
GPIO2.associatedPins[1].$name       = "AIN2";
GPIO2.associatedPins[1].assignedPin = "13";

GPIO3.$name                         = "Gray_Address";
GPIO3.port                          = "PORTB";
GPIO3.associatedPins.create(3);
GPIO3.associatedPins[0].$name       = "PIN_0";
GPIO3.associatedPins[0].pin.$assign = "PB0";
GPIO3.associatedPins[1].$name       = "PIN_1";
GPIO3.associatedPins[1].pin.$assign = "PB1";
GPIO3.associatedPins[2].$name       = "PIN_2";
GPIO3.associatedPins[2].pin.$assign = "PB2";

GPIO4.$name                         = "BIN";
GPIO4.port                          = "PORTA";
GPIO4.associatedPins.create(2);
GPIO4.associatedPins[0].$name       = "BIN1";
GPIO4.associatedPins[0].assignedPin = "16";
GPIO4.associatedPins[1].$name       = "BIN2";
GPIO4.associatedPins[1].assignedPin = "17";

GPIO5.$name                               = "ENCODERA";
GPIO5.associatedPins.create(2);
GPIO5.associatedPins[0].$name             = "E1A";
GPIO5.associatedPins[0].direction         = "INPUT";
GPIO5.associatedPins[0].interruptEn       = true;
GPIO5.associatedPins[0].interruptPriority = "0";
GPIO5.associatedPins[0].polarity          = "RISE";
GPIO5.associatedPins[0].pin.$assign       = "PA25";
GPIO5.associatedPins[1].$name             = "E1B";
GPIO5.associatedPins[1].direction         = "INPUT";
GPIO5.associatedPins[1].interruptEn       = true;
GPIO5.associatedPins[1].interruptPriority = "0";
GPIO5.associatedPins[1].polarity          = "RISE";
GPIO5.associatedPins[1].pin.$assign       = "PA26";

GPIO6.$name                               = "ENCODERB";
GPIO6.associatedPins.create(2);
GPIO6.associatedPins[0].direction         = "INPUT";
GPIO6.associatedPins[0].$name             = "E2A";
GPIO6.associatedPins[0].interruptEn       = true;
GPIO6.associatedPins[0].interruptPriority = "0";
GPIO6.associatedPins[0].polarity          = "RISE";
GPIO6.associatedPins[0].pin.$assign       = "PB20";
GPIO6.associatedPins[1].direction         = "INPUT";
GPIO6.associatedPins[1].$name             = "E2B";
GPIO6.associatedPins[1].interruptEn       = true;
GPIO6.associatedPins[1].interruptPriority = "0";
GPIO6.associatedPins[1].polarity          = "RISE";
GPIO6.associatedPins[1].pin.$assign       = "PB24";

GPIO7.$name                              = "KEY";
GPIO7.associatedPins.create(2);
GPIO7.associatedPins[0].$name            = "key";
GPIO7.associatedPins[0].direction        = "INPUT";
GPIO7.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO7.associatedPins[1].$name            = "PIN_3";
GPIO7.associatedPins[1].direction        = "INPUT";
GPIO7.associatedPins[1].internalResistor = "PULL_DOWN";
GPIO7.associatedPins[1].pin.$assign      = "PA15";

GPIO8.$name                         = "OLED";
GPIO8.port                          = "PORTB";
GPIO8.associatedPins.create(3);
GPIO8.associatedPins[0].$name       = "RES";
GPIO8.associatedPins[0].assignedPin = "15";
GPIO8.associatedPins[1].$name       = "DC";
GPIO8.associatedPins[1].assignedPin = "14";
GPIO8.associatedPins[2].$name       = "CS";
GPIO8.associatedPins[2].assignedPin = "13";

GPIO9.$name                         = "Stop";
GPIO9.associatedPins[0].$name       = "PIN_4";
GPIO9.associatedPins[0].pin.$assign = "PA0";

I2C1.$name                             = "I2C_OLED";
I2C1.basicEnableController             = true;
I2C1.basicClockSourceDivider           = 4;
I2C1.basicControllerBusSpeed           = 500000;
I2C1.peripheral.$assign                = "I2C0";
I2C1.peripheral.sdaPin.$assign         = "PA28";
I2C1.peripheral.sclPin.$assign         = "PA31";
I2C1.sdaPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sdaPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sdaPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sdaPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";
I2C1.sclPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sclPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sclPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sclPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric6";

PWM1.$name                      = "PWM_0";
PWM1.timerStartTimer            = true;
PWM1.timerCount                 = 8000;
PWM1.pwmMode                    = "EDGE_ALIGN_UP";
PWM1.peripheral.$assign         = "TIMA1";
PWM1.peripheral.ccp0Pin.$assign = "PB4";
PWM1.peripheral.ccp1Pin.$assign = "PB5";
PWM1.PWM_CHANNEL_0.$name        = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_1.$name        = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.ccp0PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric3";
PWM1.ccp1PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric4";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;
scripting.suppress("For best practices when the CPUCLK is running at 32MHz and above, clear the flash status bit using DL_FlashCTL_executeClearStatus\\(\\) before executing any flash operation\\. Otherwise there may be false positives\\.", SYSCTL);

SYSTICK.periodEnable      = true;
SYSTICK.period            = 32000;
SYSTICK.interruptEnable   = true;
SYSTICK.systickEnable     = true;
SYSTICK.interruptPriority = "0";

TIMER1.$name              = "TIMER_0";
TIMER1.timerClkPrescale   = 32;
TIMER1.timerClkDiv        = 2;
TIMER1.timerStartTimer    = true;
TIMER1.interrupts         = ["ZERO"];
TIMER1.timerMode          = "PERIODIC";
TIMER1.timerPeriod        = "125us";
TIMER1.peripheral.$assign = "TIMG0";

UART1.$name                    = "UART_0";
UART1.interruptPriority        = "1";
UART1.targetBaudRate           = 115200;
UART1.rxFifoThreshold          = "DL_UART_RX_FIFO_LEVEL_ONE_ENTRY";
UART1.enabledInterrupts        = ["RX"];
UART1.enableDMARX              = false;
UART1.peripheral.$assign       = "UART0";
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
GPIO1.associatedPins[0].pin.$suggestSolution = "PB22";
GPIO2.associatedPins[0].pin.$suggestSolution = "PA14";
GPIO2.associatedPins[1].pin.$suggestSolution = "PA13";
GPIO4.associatedPins[0].pin.$suggestSolution = "PA16";
GPIO4.associatedPins[1].pin.$suggestSolution = "PA17";
GPIO7.associatedPins[0].pin.$suggestSolution = "PB16";
GPIO8.associatedPins[0].pin.$suggestSolution = "PB15";
GPIO8.associatedPins[1].pin.$suggestSolution = "PB14";
GPIO8.associatedPins[2].pin.$suggestSolution = "PB13";
