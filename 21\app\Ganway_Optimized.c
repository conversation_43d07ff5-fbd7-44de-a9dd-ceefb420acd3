#include "Ganway_Optimized.h"
#include "bsp_system.h"

// 全局循迹控制变量初始化
Track_Control_t track_ctrl = {
    .mode = TRACK_DEFAULT_MODE,
    .state = TRACK_STATE_NORMAL,
    .base_speed = TRACK_BASE_SPEED_NORMAL,
    .line_position = CENTER_POSITION,
    .error = 0,
    .last_error = 0,
    .lost_count = 0,
    .intersection_count = 0,
    .corner_count = 0,
    .corner_recovery_count = 0,
    .last_digital = 0
};

// 传感器权重数组（用于加权位置计算）
static const float sensor_weights[SENSOR_COUNT] = {0, 1, 2, 3, 4, 5, 6, 7};

/**
 * @brief 循迹系统初始化
 */
void Track_Init(void)
{
    track_ctrl.mode = TRACK_MODE_ADAPTIVE;  // 使用自适应模式处理直角拐角
    track_ctrl.state = TRACK_STATE_NORMAL;
    track_ctrl.base_speed = TRACK_BASE_SPEED_NORMAL;
    track_ctrl.line_position = CENTER_POSITION;
    track_ctrl.error = 0;
    track_ctrl.last_error = 0;
    track_ctrl.lost_count = 0;
    track_ctrl.intersection_count = 0;
    track_ctrl.corner_count = 0;
    track_ctrl.corner_recovery_count = 0;
    track_ctrl.last_digital = 0;

    // 初始化电机PID参数
    motor_pid.kp = TRACK_PID_KP;
    motor_pid.ki = TRACK_PID_KI;
    motor_pid.kd = TRACK_PID_KD;
    motor_pid.max_integral = TRACK_PID_MAX_INTEGRAL;

    // 速度一致性初始化检查
    #if TRACK_SPEED_CONSISTENCY_CHECK
    // 重置速度监控
    Motor_Reset_Speed_Monitor();

    // 验证差速参数的合理性
    if(TRACK_TURN_DIFF >= track_ctrl.base_speed) {
        // 参数不合理，使用安全默认值
        track_ctrl.base_speed = TRACK_BASE_SPEED_SLOW;
    }
    #endif
}

/**
 * @brief 设置循迹模式
 */
void Track_Set_Mode(Track_Mode_t mode)
{
    track_ctrl.mode = mode;
}

/**
 * @brief 设置基础速度
 */
void Track_Set_Speed(int speed)
{
    if(speed >= TRACK_MIN_SPEED && speed <= TRACK_MAX_SPEED) {
        track_ctrl.base_speed = speed;
    }
}

/**
 * @brief 优化的循迹主函数
 */
void Way_Optimized(unsigned char digital_data, unsigned short *analog_data)
{
    // 分析当前状态
    track_ctrl.state = Analyze_Track_State(digital_data);
    
    // 根据模式选择控制算法
    switch(track_ctrl.mode)
    {
        case TRACK_MODE_BASIC:
            Track_Basic_Control(digital_data);
            break;
            
        case TRACK_MODE_WEIGHTED:
            Track_Weighted_Control(digital_data, analog_data);
            break;
            
        case TRACK_MODE_PID:
            Track_PID_Control(digital_data, analog_data);
            break;
            
        case TRACK_MODE_ADAPTIVE:
            Track_Adaptive_Control(digital_data, analog_data);
            break;
            
        default:
            Track_Weighted_Control(digital_data, analog_data);
            break;
    }
    
    // 更新历史数据
    track_ctrl.last_digital = digital_data;
}

/**
 * @brief 计算黑线位置（加权算法）
 */
float Calculate_Line_Position(unsigned char digital_data, unsigned short *analog_data)
{
    float weighted_sum = 0.0f;
    float total_weight = 0.0f;
    
    // 使用模拟量进行加权计算，提高精度
    for(int i = 0; i < SENSOR_COUNT; i++)
    {
        // 反转数字位（1表示检测到黑线）
        unsigned char sensor_state = (digital_data >> i) & 0x01;
        sensor_state = 1 - sensor_state;  // 反转逻辑
        
        if(sensor_state) {
            // 使用模拟量作为权重，提高精度
            float weight = (float)analog_data[i];
            weighted_sum += sensor_weights[i] * weight;
            total_weight += weight;
        }
    }
    
    // 如果没有检测到黑线，返回上次位置
    if(total_weight == 0.0f) {
        return track_ctrl.line_position;
    }
    
    return weighted_sum / total_weight;
}

/**
 * @brief 计算位置误差
 */
int Calculate_Position_Error(float line_position)
{
    // 计算相对于中心位置的误差
    float error_float = (line_position - CENTER_POSITION) * WEIGHT_FACTOR;
    return (int)error_float;
}

/**
 * @brief 分析循迹状态（增强直角拐角检测）
 */
Track_State_t Analyze_Track_State(unsigned char digital_data)
{
    unsigned char sensor_count = 0;

    // 统计检测到黑线的传感器数量
    for(int i = 0; i < SENSOR_COUNT; i++) {
        if(!((digital_data >> i) & 0x01)) {  // 0表示检测到黑线
            sensor_count++;
        }
    }

    // 状态判断逻辑
    if(sensor_count == 0) {
        track_ctrl.lost_count++;
        if(track_ctrl.lost_count > 5) {
            return TRACK_STATE_LOST;
        }
    } else {
        track_ctrl.lost_count = 0;
    }

    // 检测十字路口（多个传感器同时检测到）
    if(sensor_count >= 6) {
        return TRACK_STATE_INTERSECTION;
    }

    // 检测直角拐角（针对正方形赛道优化）
    Track_State_t corner_state = Detect_Corner_State(digital_data);
    if(corner_state != TRACK_STATE_NORMAL) {
        return corner_state;
    }

    // 检测普通转弯
    if((digital_data & 0x07) == 0x00) {  // 左侧3个传感器都检测到
        return TRACK_STATE_TURN_LEFT;
    }
    if((digital_data & 0xE0) == 0x00) {  // 右侧3个传感器都检测到
        return TRACK_STATE_TURN_RIGHT;
    }

    return TRACK_STATE_NORMAL;
}

/**
 * @brief 基础控制模式（兼容原有逻辑）
 */
void Track_Basic_Control(unsigned char digital_data)
{
    // 调用原有的Way函数逻辑，但使用优化后的电机控制
    unsigned char sensor0 = 1-(digital_data >> 0) & 0x01;
    unsigned char sensor1 = 1-(digital_data >> 1) & 0x01;
    unsigned char sensor2 = 1-(digital_data >> 2) & 0x01;
    unsigned char sensor3 = 1-(digital_data >> 3) & 0x01;
    unsigned char sensor4 = 1-(digital_data >> 4) & 0x01;
    unsigned char sensor5 = 1-(digital_data >> 5) & 0x01;
    unsigned char sensor6 = 1-(digital_data >> 6) & 0x01;
    unsigned char sensor7 = 1-(digital_data >> 7) & 0x01;

    // 全黑（丢线）处理
    if(sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && 
       sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
        Handle_Lost_Line();
        return;
    }
    
    // 中心位置（直行）- 确保使用统一的速度基准
    if(sensor3 == 1 && sensor4 == 1) {
        Set_PWM(track_ctrl.base_speed, track_ctrl.base_speed);
        return;
    }
    
    // 左转控制
    if(sensor0 == 1 || sensor1 == 1 || sensor2 == 1) {
        Left_Control();
    }
    // 右转控制
    else if(sensor5 == 1 || sensor6 == 1 || sensor7 == 1) {
        Right_Control();
    }
    // 微调控制
    else if(sensor3 == 1) {
        Left_Little_Control();
    }
    else if(sensor4 == 1) {
        Right_Little_Control();
    }
}

/**
 * @brief 加权位置控制模式
 */
void Track_Weighted_Control(unsigned char digital_data, unsigned short *analog_data)
{
    // 计算黑线位置
    track_ctrl.line_position = Calculate_Line_Position(digital_data, analog_data);
    
    // 计算误差
    track_ctrl.error = Calculate_Position_Error(track_ctrl.line_position);
    
    // 使用平滑控制
    Motor_Smooth_Control(track_ctrl.error, track_ctrl.base_speed);
    
    // 更新历史误差
    track_ctrl.last_error = track_ctrl.error;
}

/**
 * @brief PID控制模式
 */
void Track_PID_Control(unsigned char digital_data, unsigned short *analog_data)
{
    // 计算黑线位置
    track_ctrl.line_position = Calculate_Line_Position(digital_data, analog_data);
    
    // 计算误差
    track_ctrl.error = Calculate_Position_Error(track_ctrl.line_position);
    
    // 使用PID控制
    Motor_PID_Control(track_ctrl.error, track_ctrl.base_speed);
    
    // 更新历史误差
    track_ctrl.last_error = track_ctrl.error;
}

/**
 * @brief 自适应控制模式（增强直角拐角处理）
 */
void Track_Adaptive_Control(unsigned char digital_data, unsigned short *analog_data)
{
    // 根据当前状态自适应选择控制策略
    switch(track_ctrl.state)
    {
        case TRACK_STATE_NORMAL:
            // 正常状态，恢复正常速度
            if(track_ctrl.corner_recovery_count > 0) {
                track_ctrl.corner_recovery_count--;
                track_ctrl.base_speed = TRACK_BASE_SPEED_NORMAL * 0.8f;  // 渐进恢复
            } else {
                track_ctrl.base_speed = TRACK_BASE_SPEED_NORMAL;
            }
            Track_PID_Control(digital_data, analog_data);
            break;

        case TRACK_STATE_CORNER_LEFT:
        case TRACK_STATE_CORNER_RIGHT:
            // 直角拐角：大幅降速，使用专用处理
            track_ctrl.base_speed = TRACK_BASE_SPEED_NORMAL * TRACK_CORNER_SPEED_RATIO;
            Handle_Corner_Turn(track_ctrl.state == TRACK_STATE_CORNER_LEFT ? -1 : 1);
            track_ctrl.corner_recovery_count = TRACK_CORNER_RECOVERY_DELAY / 10;  // 设置恢复计数
            break;

        case TRACK_STATE_TURN_LEFT:
        case TRACK_STATE_TURN_RIGHT:
            // 普通转弯时降低速度
            track_ctrl.base_speed = TRACK_BASE_SPEED_NORMAL * TRACK_ADAPTIVE_SPEED_RATIO;
            Track_PID_Control(digital_data, analog_data);
            break;

        case TRACK_STATE_INTERSECTION:
            Handle_Intersection();
            break;

        case TRACK_STATE_LOST:
            Handle_Lost_Line();
            break;

        default:
            Track_Weighted_Control(digital_data, analog_data);
            break;
    }
}

/**
 * @brief 处理丢线情况（使用统一速度标准）
 */
void Handle_Lost_Line(void)
{
    // 使用基于base_speed的搜索策略，避免硬编码PWM值
    int search_speed = track_ctrl.base_speed * 0.6f;  // 降低搜索速度
    int search_diff = TRACK_TURN_DIFF_LARGE;

    // 根据上次误差方向进行搜索
    if(track_ctrl.last_error > 0) {
        // 上次偏右，向右搜索
        Set_PWM(search_speed - search_diff, search_speed);
    } else if(track_ctrl.last_error < 0) {
        // 上次偏左，向左搜索
        Set_PWM(search_speed, search_speed - search_diff);
    } else {
        // 无历史信息，缓慢前进
        Set_PWM(search_speed, search_speed);
    }
}

/**
 * @brief 处理十字路口
 */
void Handle_Intersection(void)
{
    // 简单处理：直行通过
    Set_PWM(track_ctrl.base_speed, track_ctrl.base_speed);
    delay_ms(100);  // 短暂延时通过路口
}

/**
 * @brief 处理急转弯（使用统一速度标准）
 */
void Handle_Sharp_Turn(int direction)
{
    // 使用基于base_speed的急转弯控制
    int turn_speed = track_ctrl.base_speed * 0.8f;  // 急转弯时适当降速
    int turn_diff = TRACK_TURN_DIFF_LARGE;

    if(direction > 0) {
        // 右转：左轮保持速度，右轮减速
        Set_PWM(turn_speed - turn_diff, turn_speed);
    } else {
        // 左转：右轮保持速度，左轮减速
        Set_PWM(turn_speed, turn_speed - turn_diff);
    }
}

/**
 * @brief 调试信息输出
 */
void Track_Debug_Info(void)
{
#if TRACK_DEBUG_ENABLE && TRACK_DEBUG_OLED_ENABLE
    // 这里可以添加OLED显示调试信息的代码
    // 例如显示当前模式、线位置、误差等
    // OLED_ShowString(0, 0, "Mode:", 12, 1);
    // OLED_ShowNum(30, 0, track_ctrl.mode, 1, 12, 1);
    // OLED_ShowString(0, 12, "Pos:", 12, 1);
    // OLED_ShowSignedNum(24, 12, (int)(track_ctrl.line_position * 10), 2, 12, 1);
    // OLED_ShowString(0, 24, "Err:", 12, 1);
    // OLED_ShowSignedNum(24, 24, track_ctrl.error, 3, 12, 1);
    // OLED_Refresh();
#endif

#if TRACK_DEBUG_ENABLE && TRACK_DEBUG_UART_ENABLE
    // 这里可以添加串口输出调试信息的代码
    // printf("Mode:%d Pos:%.2f Err:%d\r\n",
    //        track_ctrl.mode, track_ctrl.line_position, track_ctrl.error);
#endif
}

/**
 * @brief 传感器校准函数
 */
void Track_Calibrate_Sensors(No_MCU_Sensor *sensor)
{
#if TRACK_AUTO_CALIBRATE_ENABLE
    // 自动校准逻辑
    // 这里可以添加自动校准的实现
    // 例如：采集多组数据，计算平均值等
#endif
}

/**
 * @brief 获取当前循迹状态信息（用于外部监控）
 */
Track_Control_t* Track_Get_Status(void)
{
    return &track_ctrl;
}

/**
 * @brief 设置循迹参数（运行时调整）
 */
void Track_Set_PID_Params(float kp, float ki, float kd)
{
    motor_pid.kp = kp;
    motor_pid.ki = ki;
    motor_pid.kd = kd;
}

/**
 * @brief 重置PID积分项（用于消除积分饱和）
 */
void Track_Reset_PID_Integral(void)
{
    motor_pid.integral = 0;
}

/**
 * @brief 紧急停止函数
 */
void Track_Emergency_Stop(void)
{
    Motor_Stop();
    track_ctrl.state = TRACK_STATE_STOP;
}

/**
 * @brief 直角拐角检测函数（针对正方形赛道）
 */
Track_State_t Detect_Corner_State(unsigned char digital_data)
{
    // 检测直角左拐角：左侧传感器大量检测到黑线
    if((digital_data & 0x0F) == 0x00) {  // 左侧4个传感器都检测到黑线
        track_ctrl.corner_count++;
        if(track_ctrl.corner_count >= TRACK_CORNER_DETECTION_COUNT) {
            track_ctrl.corner_count = 0;
            return TRACK_STATE_CORNER_LEFT;
        }
    }
    // 检测直角右拐角：右侧传感器大量检测到黑线
    else if((digital_data & 0xF0) == 0x00) {  // 右侧4个传感器都检测到黑线
        track_ctrl.corner_count++;
        if(track_ctrl.corner_count >= TRACK_CORNER_DETECTION_COUNT) {
            track_ctrl.corner_count = 0;
            return TRACK_STATE_CORNER_RIGHT;
        }
    }
    else {
        // 重置拐角计数
        track_ctrl.corner_count = 0;
    }

    return TRACK_STATE_NORMAL;
}

/**
 * @brief 直角拐角处理函数（专用于正方形赛道）
 */
void Handle_Corner_Turn(int direction)
{
    // 直角拐角需要更激进的转向策略
    int corner_speed = track_ctrl.base_speed;
    int corner_diff = TRACK_TURN_DIFF_LARGE;

    if(direction > 0) {
        // 右拐角：左轮保持速度，右轮大幅减速或停止
        Set_PWM(corner_speed - corner_diff, corner_speed * 0.3f);
    } else {
        // 左拐角：右轮保持速度，左轮大幅减速或停止
        Set_PWM(corner_speed * 0.3f, corner_speed - corner_diff);
    }
}
