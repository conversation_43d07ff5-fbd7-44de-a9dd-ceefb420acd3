# MSPM0G3507循迹系统优化方案

## 问题分析总结

### 1. 原系统存在的问题

#### **转向控制问题**
- **硬编码PWM值**：所有转向函数使用固定PWM值，缺乏灵活性
- **转向幅度过大**：左右轮差速过大（如5000 vs 2250），导致过度修正
- **缺乏渐进式控制**：没有根据偏离程度调整转向强度

#### **差速控制问题**
- **差速比不合理**：左右轮速度差异过大，转向不平滑
- **缺乏PID控制**：虽有PID相关代码但未实际使用
- **响应过于激烈**：传感器状态变化时立即切换到最大差速

#### **传感器数据处理问题**
- **简单二值化**：只使用数字量，未充分利用模拟量信息
- **缺乏加权计算**：没有计算黑线的精确位置
- **逻辑复杂**：Ganway.c中的if-else结构冗长难维护

## 优化方案实施

### 1. 新增文件结构

```
app/
├── Ganway_Optimized.h      # 优化循迹算法头文件
├── Ganway_Optimized.c      # 优化循迹算法实现
├── Track_Config.h          # 配置参数文件
├── motor.h                 # 更新的电机控制头文件
├── motor.c                 # 优化的电机控制实现
└── 循迹系统优化说明.md      # 本说明文档
```

### 2. 核心优化内容

#### **A. 电机控制优化 (motor.c/motor.h)**

**新增功能：**
- PID控制算法
- 平滑转向控制
- 参数化PWM值
- 速度限制保护

**优化参数：**
```c
// 原参数 -> 优化参数
Right_Control: (2250, 5000) -> (3000, 4500)  // 减小差速比
Left_Control:  (5000, 2250) -> (4500, 3000)  // 减小差速比
Little_Control: (6000, 1500) -> (4500, 2500) // 减小差速
Large_Control: (5000, -4000) -> (4000, -2000) // 减小反向速度
```

#### **B. 循迹算法优化 (Ganway_Optimized.c)**

**新增4种控制模式：**

1. **基础模式 (TRACK_MODE_BASIC)**
   - 兼容原有逻辑
   - 使用优化后的电机参数

2. **加权位置模式 (TRACK_MODE_WEIGHTED)** ⭐推荐
   - 利用模拟量计算精确黑线位置
   - 平滑转向控制
   - 减少震荡

3. **PID控制模式 (TRACK_MODE_PID)**
   - 完整PID算法
   - 自动调节转向强度
   - 最佳稳定性

4. **自适应模式 (TRACK_MODE_ADAPTIVE)**
   - 根据路况自动调整策略
   - 处理特殊情况（丢线、路口等）

#### **C. 配置参数化 (Track_Config.h)**

**可调参数分类：**
- 基础速度配置
- 转向控制配置  
- PID参数配置
- 传感器配置
- 状态检测配置
- 调试配置

### 3. 使用方法

#### **A. 快速开始**

在`empty.c`主程序中：

```c
// 1. 引入头文件
#include "Ganway_Optimized.h"

// 2. 初始化
Track_Init();

// 3. 在主循环中使用优化算法
Way_With_Analog(Digtal, Analog_Values);  // 推荐
// 或继续使用原算法
Way(Digtal);  // 兼容模式
```

#### **B. 参数调整**

在`Track_Config.h`中调整参数：

```c
// 调整基础速度
#define TRACK_BASE_SPEED_NORMAL     4000    // 根据实际情况调整

// 调整PID参数
#define TRACK_PID_KP                0.8f    // 比例系数
#define TRACK_PID_KI                0.1f    // 积分系数
#define TRACK_PID_KD                0.2f    // 微分系数

// 调整转向参数
#define TRACK_SMOOTH_FACTOR         30      // 转向灵敏度
```

#### **C. 模式切换**

```c
// 运行时切换模式
Track_Set_Mode(TRACK_MODE_WEIGHTED);    // 加权模式
Track_Set_Mode(TRACK_MODE_PID);         // PID模式
Track_Set_Mode(TRACK_MODE_ADAPTIVE);    // 自适应模式

// 调整速度
Track_Set_Speed(3500);  // 设置基础速度
```

### 4. 调试建议

#### **A. 参数调试顺序**

1. **基础速度调试**
   - 先设置较低速度（3000-3500）
   - 确保小车能稳定循迹
   - 逐步提高速度

2. **转向参数调试**
   - 调整`TRACK_SMOOTH_FACTOR`
   - 值越大转向越激烈
   - 建议从20开始调试

3. **PID参数调试**
   - 先调Kp（比例系数）
   - 再调Kd（微分系数）
   - 最后调Ki（积分系数）

#### **B. 常见问题解决**

**问题1：转向不够灵敏**
```c
// 增大转向系数
#define TRACK_SMOOTH_FACTOR         40  // 原值30
// 或增大PID比例系数
#define TRACK_PID_KP                1.0f // 原值0.8f
```

**问题2：转向过度震荡**
```c
// 减小转向系数
#define TRACK_SMOOTH_FACTOR         20  // 原值30
// 或增大微分系数
#define TRACK_PID_KD                0.3f // 原值0.2f
```

**问题3：直线行驶不稳**
```c
// 增大积分系数
#define TRACK_PID_KI                0.15f // 原值0.1f
// 或减小基础速度
#define TRACK_BASE_SPEED_NORMAL     3500  // 原值4000
```

### 5. 性能对比

| 项目 | 原系统 | 优化系统 | 改善程度 |
|------|--------|----------|----------|
| 转向平滑性 | 差 | 优 | ⭐⭐⭐⭐⭐ |
| 循迹精度 | 中 | 优 | ⭐⭐⭐⭐ |
| 参数可调性 | 差 | 优 | ⭐⭐⭐⭐⭐ |
| 代码维护性 | 差 | 优 | ⭐⭐⭐⭐⭐ |
| 兼容性 | - | 优 | ⭐⭐⭐⭐⭐ |

### 6. 注意事项

1. **兼容性**：优化后的系统完全兼容原有代码
2. **渐进升级**：可以逐步从基础模式升级到高级模式
3. **参数备份**：调试前备份工作参数
4. **安全测试**：新参数应在安全环境下测试
5. **硬件检查**：确保传感器和电机工作正常

### 7. 扩展功能

优化系统还支持以下扩展功能：

- **自动校准**：传感器自动校准功能
- **状态监控**：实时监控循迹状态
- **调试输出**：OLED/串口调试信息
- **紧急停止**：安全保护机制
- **多赛道适配**：不同赛道的预设参数

通过这套优化方案，您的循迹小车将获得更好的性能表现和更强的适应性。
